# DODO added #32839638

import logging
from typing import Any

from flask_appbuilder.security.sqla.models import User
from flask_babel import lazy_gettext as _
from sqlalchemy import or_
from sqlalchemy.orm.query import Query

from superset.views.base import BaseFilter

logger = logging.getLogger(__name__)


class UserIDFilter(BaseFilter):  # pylint: disable=too-few-public-methods
    name = _("id")
    arg_name = "eq_id_user"

    def apply(self, query: Query, value: Any) -> Query:
        if value:
            return query.filter(User.id == int(value))
        return query

class UserNameFilter(BaseFilter):  # pylint: disable=too-few-public-methods
    name = _("Name")
    arg_name = "usr_name"

    def apply(self, query: Query, value: Any) -> Query:
        if not value:
            return query
        ilike_value = f"%{value}%"
        return query.filter(
            or_(
                User.first_name.ilike(ilike_value),
                User.last_name.ilike(ilike_value),
            )
        )
    
class UserLastNameFilter(BaseFilter):  # pylint: disable=too-few-public-methods
    name = _("Last Name")
    arg_name = "usr_last_name"

    def apply(self, query: Query, value: Any) -> Query:
        if not value:
            return query
        ilike_value = f"%{value}%"
        return query.filter(User.last_name.ilike(ilike_value))


class UserEmailFilter(BaseFilter):  # pylint: disable=too-few-public-methods
    name = _("Email")
    arg_name = "usr_email"

    def apply(self, query: Query, value: Any) -> Query:
        if not value:
            return query
        ilike_value = f"%{value}%"
        return query.filter(User.email.ilike(ilike_value))


class UserUsernameFilter(BaseFilter):  # pylint: disable=too-few-public-methods
    name = _("Username")
    arg_name = "usr_username"

    def apply(self, query: Query, value: Any) -> Query:
        if not value:
            return query
        ilike_value = f"%{value}%"
        return query.filter(User.username.ilike(ilike_value))


class UserDodoRoleFilter(BaseFilter):  # pylint: disable=too-few-public-methods
    name = _("Dodo role")
    arg_name = "usr_dodo_role"

    def apply(self, query: Query, value: Any) -> Query:
        if not value:
            return query
        ilike_value = f"%{value}%"
        return query.filter(User.user_info.dodo_role.ilike(ilike_value))


class UserTeamFilter(BaseFilter):  # pylint: disable=too-few-public-methods
    name = _("Team")
    arg_name = "usr_team"

    def apply(self, query: Query, value: Any) -> Query:
        if not value:
            return query
        ilike_value = f"%{value}%"
        return query.filter(User.teams.name.ilike(ilike_value))


class UserIsActiveFilter(BaseFilter):  # pylint: disable=too-few-public-methods
    name = _("Active")
    arg_name = "usr_is_active"

    def apply(self, query: Query, value: Any) -> Query:
        if not value == 0:
            if not value:
                return query
        is_active = bool(int(value))
        return query.filter(User.is_active.is_(is_active))
