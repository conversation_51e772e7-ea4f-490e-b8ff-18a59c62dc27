import logging

from flask_appbuilder.models.sqla.interface import SQLAInterface
from flask_appbuilder.security.sqla.models import User

from superset.constants import MODEL_API_RW_METHOD_PERMISSION_MAP, RouteMethod
from superset.user.filters import UserDodoRole<PERSON>ilter, UserIDFilter, UserIsActiveFilter, UserLastNameFilter, UserNameFilter, UserEmailFilter, UserTeamFilter, UserUsernameFilter
from superset.user.schemas import UserSchema
from superset.views.base_api import (
    BaseSupersetModelRestApi,
)

logger = logging.getLogger(__name__)


class DodoUserRestApi(BaseSupersetModelRestApi):
    """An api to get information about the user"""

    datamodel = SQLAInterface(User)

    include_route_methods = RouteMethod.REST_MODEL_VIEW_CRUD_SET
    resource_name = "dodo_user"
    allow_browser_login = True

    class_permission_name = "User"
    method_permission_name = MODEL_API_RW_METHOD_PERMISSION_MAP

    search_columns = ("id", "first_name", "last_name", "email", "username", "is_active", "teams", "user_info.dodo_role")

    search_filters = {
        "id": [UserIDFilter],
        "first_name": [UserNameFilter],
        "last_name": [UserLastNameFilter],
        "email": [UserEmailFilter],
        "username": [UserUsernameFilter],
        "is_active": [UserIsActiveFilter],
        "teams": [UserTeamFilter],
        "user_info.dodo_role": [UserDodoRoleFilter],
    }

    list_columns = [
        "id",
        "username",
        "first_name",
        "last_name",
        "email",
        "is_active",
        "created_on",
        "last_login",
        "login_count",
        "teams.name",
        "user_info.country_name",
        "user_info.dodo_role",
        "user_info.is_onboarding_finished",
        "roles.name",
    ]

    order_columns = [
        "id",
        "username",
        "first_name",
        "last_name",
        "email",
        "is_active",
        "created_on",
        "last_login",
        "login_count",
    ]

    user_get_response_schema = UserSchema()
